import { CapacitorHttp } from '@capacitor/core';

interface UploadOptions {
  fileName: string;
  mimeType?: string;
  content: any;
  appDataFolder?: boolean;
}

interface UpdateOptions extends UploadOptions {
  fileId: string;
}

interface FindOptions {
  fileId?: string;
  spaces?: string;
  orderBy?: string;
}

interface ServiceResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

class GoogleDrive {
  private readonly GOOGLE_DRIVE_API_URL = 'https://www.googleapis.com/drive/v3';
  private accessToken: string | null = null;

  async initialize({ accessToken }: { accessToken: string }) {
    this.accessToken = accessToken;
  }

  async upload(options: UploadOptions): Promise<ServiceResponse> {
    const { fileName, mimeType, content, appDataFolder } = options;

    if (!this.accessToken) throw new Error('No access token');
    if (!fileName) throw new Error('No file name');
    if (!content) throw new Error('No file content');

    const metadata = {
      name: fileName,
      ...(mimeType && { mimeType }),
      ...(appDataFolder && { parents: ['appDataFolder'] }),
    };

    const form = new FormData();
    form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }));
    form.append('file', new Blob([content], { type: mimeType }));

    try {
      const response = await CapacitorHttp.post({
        url: `${this.GOOGLE_DRIVE_API_URL}/files?uploadType=multipart&fields=id,name,mimeType,size`,
        headers: this.getHeaders(),
        data: form,
      });

      if (response.status !== 200) {
        throw new Error(`Failed to upload file: ${response.status} ${response.data?.error_description || ''}`);
      }

      return { success: true, message: 'File uploaded successfully', data: response.data };
    } catch (e: any) {
      console.log(e);
      return { success: false, message: 'An error when upload file', error: e };
    }
  }

  async download(fileId: string): Promise<ServiceResponse> {
    if (!this.accessToken) throw new Error('No access token');
    if (!fileId) throw new Error('No file id');

    try {
      const response = await CapacitorHttp.get({
        url: `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}?alt=media`,
        headers: this.getHeaders(),
      });

      if (response.status !== 200) {
        throw new Error(`Failed to download file: ${response.status} ${response.data?.error_description || ''}`);
      }

      return { success: true, message: 'File downloaded successfully', data: response.data };
    } catch (e: any) {
      console.log(e);
      return { success: false, message: 'An error when download file', error: e };
    }
  }

  async update(options: UpdateOptions): Promise<ServiceResponse> {
    const { fileId, fileName, mimeType, content, appDataFolder } = options;

    if (!this.accessToken) throw new Error('No access token');
    if (!fileId) throw new Error('No file id');
    if (!fileName) throw new Error('No file name');
    if (!content) throw new Error('No file content');

    const metadata = {
      name: fileName,
      ...(mimeType && { mimeType }),
      ...(appDataFolder && { parents: ['appDataFolder'] }),
    };

    const form = new FormData();
    form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }));
    form.append('file', new Blob([content], { type: mimeType }));

    try {
      const response = await CapacitorHttp.put({
        url: `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}?uploadType=multipart`,
        headers: this.getHeaders(),
        data: form,
      });

      if (response.status !== 200) {
        throw new Error(`Failed to update file: ${response.status} ${response.data?.error_description || ''}`);
      }

      return { success: true, message: 'File updated successfully', data: response.data };
    } catch (e: any) {
      console.log(e);
      return { success: false, message: 'An error when update file', error: e };
    }
  }

  async delete(fileId: string): Promise<ServiceResponse> {
    if (!this.accessToken) throw new Error('No access token');
    if (!fileId) throw new Error('No file id');

    try {
      const response = await CapacitorHttp.delete({
        url: `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}`,
        headers: this.getHeaders(),
      });

      if (response.status !== 204) {
        throw new Error(`Failed to delete file: ${response.status} ${response.data?.error_description || ''}`);
      }

      return { success: true, message: 'File deleted successfully' };
    } catch (e: any) {
      console.log(e);
      return { success: false, message: 'An error when delete file', error: e };
    }
  }

  async find(options: FindOptions): Promise<ServiceResponse> {
    const { fileId, spaces = 'drive', orderBy = 'createdTime desc' } = options;

    if (!this.accessToken) throw new Error('No access token');

    const API = fileId
      ? `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}?fields=id,name,mimeType,size`
      : `${this.GOOGLE_DRIVE_API_URL}/files?spaces=${spaces}&orderBy=${orderBy}`;

    try {
      const response = await CapacitorHttp.get({
        url: API,
        headers: this.getHeaders(),
      });

      if (response.status !== 200) {
        throw new Error(`Failed to find file: ${response.status} ${response.data?.error_description || ''}`);
      }

      return { success: true, message: 'File found successfully', data: response.data };
    } catch (e: any) {
      console.log(e);
      return { success: false, message: 'An error when find file', error: e };
    }
  }

  private getHeaders() {
    return {
      Authorization: `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json',
    };
  }
}

export const googleDrive = new GoogleDrive();
